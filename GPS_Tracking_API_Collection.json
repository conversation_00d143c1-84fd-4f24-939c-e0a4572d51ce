{"info": {"name": "GPS Tracker API Collection", "description": "Complete API collection for GPS tracking with real-time path updates", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "GPS API Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/gps/health", "host": ["{{baseUrl}}"], "path": ["api", "gps", "health"]}}, "response": []}, {"name": "Update Device Location (Kadapa)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"deviceId\": \"QR112562854\",\n  \"deviceName\": \"Puppy GPS Tracker\",\n  \"latitude\": 14.4673,\n  \"longitude\": 78.8242,\n  \"accuracy\": 5,\n  \"speed\": 2.5,\n  \"heading\": 45\n}"}, "url": {"raw": "{{baseUrl}}/api/gps/location", "host": ["{{baseUrl}}"], "path": ["api", "gps", "location"]}}, "response": []}, {"name": "Update Device Location (Proddatur)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"deviceId\": \"QR112562854\",\n  \"deviceName\": \"Puppy GPS Tracker\",\n  \"latitude\": 14.7300,\n  \"longitude\": 78.5500,\n  \"accuracy\": 3,\n  \"speed\": 1.8,\n  \"heading\": 90\n}"}, "url": {"raw": "{{baseUrl}}/api/gps/location", "host": ["{{baseUrl}}"], "path": ["api", "gps", "location"]}}, "response": []}, {"name": "Move Device North", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"deviceId\": \"QR112562854\",\n  \"deviceName\": \"Puppy GPS Tracker\",\n  \"latitude\": 14.7310,\n  \"longitude\": 78.5500,\n  \"accuracy\": 4,\n  \"speed\": 3.2,\n  \"heading\": 0\n}"}, "url": {"raw": "{{baseUrl}}/api/gps/location", "host": ["{{baseUrl}}"], "path": ["api", "gps", "location"]}}, "response": []}, {"name": "Move Device East", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"deviceId\": \"QR112562854\",\n  \"deviceName\": \"Puppy GPS Tracker\",\n  \"latitude\": 14.7310,\n  \"longitude\": 78.5510,\n  \"accuracy\": 6,\n  \"speed\": 2.1,\n  \"heading\": 90\n}"}, "url": {"raw": "{{baseUrl}}/api/gps/location", "host": ["{{baseUrl}}"], "path": ["api", "gps", "location"]}}, "response": []}, {"name": "Move Device South", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"deviceId\": \"QR112562854\",\n  \"deviceName\": \"Puppy GPS Tracker\",\n  \"latitude\": 14.7300,\n  \"longitude\": 78.5510,\n  \"accuracy\": 5,\n  \"speed\": 1.5,\n  \"heading\": 180\n}"}, "url": {"raw": "{{baseUrl}}/api/gps/location", "host": ["{{baseUrl}}"], "path": ["api", "gps", "location"]}}, "response": []}, {"name": "Get Device Current Location", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/gps/device/QR112562854", "host": ["{{baseUrl}}"], "path": ["api", "gps", "device", "QR112562854"]}}, "response": []}, {"name": "Get Device Path History", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/gps/path/QR112562854", "host": ["{{baseUrl}}"], "path": ["api", "gps", "path", "QR112562854"]}}, "response": []}, {"name": "Get Device Path (Last 10 Points)", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/gps/path/QR112562854?limit=10", "host": ["{{baseUrl}}"], "path": ["api", "gps", "path", "QR112562854"], "query": [{"key": "limit", "value": "10"}]}}, "response": []}, {"name": "Get All Tracked Devices", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/gps/devices", "host": ["{{baseUrl}}"], "path": ["api", "gps", "devices"]}}, "response": []}, {"name": "Simulate Device Movement", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"startLat\": 14.7300,\n  \"startLng\": 78.5500,\n  \"endLat\": 14.7350,\n  \"endLng\": 78.5550,\n  \"steps\": 15,\n  \"deviceName\": \"Puppy GPS Tracker\"\n}"}, "url": {"raw": "{{baseUrl}}/api/gps/simulate/QR112562854", "host": ["{{baseUrl}}"], "path": ["api", "gps", "simulate", "QR112562854"]}}, "response": []}, {"name": "Clear Device Data", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/gps/device/QR112562854", "host": ["{{baseUrl}}"], "path": ["api", "gps", "device", "QR112562854"]}}, "response": []}], "variable": [{"key": "baseUrl", "value": "http://localhost:5001", "type": "string"}]}