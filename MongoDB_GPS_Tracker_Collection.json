{"info": {"name": "GPS Tracker - Complete MongoDB API", "description": "Complete MongoDB-integrated GPS Tracker API with authentication, devices, QR codes, and location tracking", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:5001", "type": "string"}, {"key": "authToken", "value": "", "type": "string"}], "item": [{"name": "🏥 Health & Status", "item": [{"name": "Health Check - MongoDB", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/health", "host": ["{{baseUrl}}"], "path": ["api", "health"]}}}]}, {"name": "🔐 Authentication", "item": [{"name": "Register New User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"testuser\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\",\n  \"firstName\": \"Test\",\n  \"lastName\": \"User\",\n  \"company\": \"GPS Tracker Inc\",\n  \"phone\": \"+91 **********\",\n  \"role\": \"user\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/register", "host": ["{{baseUrl}}"], "path": ["api", "auth", "register"]}}}, {"name": "Login User", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.token) {", "        pm.collectionVariables.set('authToken', response.token);", "        console.log('Auth token saved:', response.token);", "    }", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/login", "host": ["{{baseUrl}}"], "path": ["api", "auth", "login"]}}}, {"name": "Register Admin User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"admin\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"admin123\",\n  \"firstName\": \"GPS\",\n  \"lastName\": \"Admin\",\n  \"company\": \"GPS Tracker Inc\",\n  \"phone\": \"+91 **********\",\n  \"role\": \"admin\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/register", "host": ["{{baseUrl}}"], "path": ["api", "auth", "register"]}}}]}, {"name": "👥 User Management", "item": [{"name": "Get All Users (Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/users", "host": ["{{baseUrl}}"], "path": ["api", "users"]}}}]}, {"name": "📱 Device Management", "item": [{"name": "Get All Devices", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/devices", "host": ["{{baseUrl}}"], "path": ["api", "devices"]}}}, {"name": "Create New Device", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"GPS Tracker Pro\",\n  \"type\": \"gps\",\n  \"model\": \"GT-2024\",\n  \"description\": \"High-precision GPS tracking device\",\n  \"serialNumber\": \"GT2024001\",\n  \"category\": \"Vehicle Tracker\"\n}"}, "url": {"raw": "{{baseUrl}}/api/devices", "host": ["{{baseUrl}}"], "path": ["api", "devices"]}}}]}, {"name": "🔲 QR Code Management", "item": [{"name": "Generate QR Codes", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"count\": 3,\n  \"deviceInfo\": {\n    \"deviceName\": \"GPS Tracker Device\",\n    \"deviceType\": \"gps\",\n    \"deviceModel\": \"GT-2024\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/qr/generate", "host": ["{{baseUrl}}"], "path": ["api", "qr", "generate"]}}}, {"name": "Get All QR Codes", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/qr", "host": ["{{baseUrl}}"], "path": ["api", "qr"]}}}]}, {"name": "📍 Location Tracking", "item": [{"name": "Update Device Location", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"deviceId\": \"DEVICE_ID_HERE\",\n  \"latitude\": 14.7504,\n  \"longitude\": 78.5569,\n  \"accuracy\": 10,\n  \"address\": \"Proddatur, Andhra Pradesh, India\"\n}"}, "url": {"raw": "{{baseUrl}}/api/locations", "host": ["{{baseUrl}}"], "path": ["api", "locations"]}}}]}, {"name": "📊 Dashboard & Analytics", "item": [{"name": "Dashboard Statistics", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/dashboard/stats", "host": ["{{baseUrl}}"], "path": ["api", "dashboard", "stats"]}}}, {"name": "Recent Activities", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/activities", "host": ["{{baseUrl}}"], "path": ["api", "activities"]}}}]}]}