# Google OAuth Configuration
# Get your Google Client ID from: https://console.developers.google.com/
# 1. Create a new project or select existing project
# 2. Enable Google+ API
# 3. Create OAuth 2.0 credentials
# 4. Add your domain to authorized origins
REACT_APP_GOOGLE_CLIENT_ID=your_google_client_id_here.apps.googleusercontent.com

# For development, you can use localhost
# Authorized JavaScript origins: http://localhost:3000
# Authorized redirect URIs: http://localhost:3000
