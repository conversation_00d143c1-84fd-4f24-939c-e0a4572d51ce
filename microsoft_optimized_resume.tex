%%%%
% Microsoft-Optimized Resume for T <PERSON>man Yadav
%%%%
% Author: <PERSON> (Template)
% License: CC-BY-4
% - https://creativecommons.org/licenses/by/4.0/legalcode.txt
%%%%

\documentclass[letterpaper,8pt]{article}
%%%%%%%%%%%%%%%%%%%%%%%
%% BEGIN_FILE: mteck.sty
%% NOTE: Everything between here and END_FILE can
%% be relocated to "mteck.sty" and then included with:
%\usepackage{mteck}

% Dependencies
\usepackage[empty]{fullpage}
\usepackage{titlesec}
\usepackage{enumitem}
\usepackage[hidelinks]{hyperref}
\usepackage{fancyhdr}
\usepackage{fontawesome5}
\usepackage{multicol}
\usepackage{bookmark}
\usepackage{lastpage}

% Sans-Serif
\usepackage[sfdefault]{FiraSans}

% Serif
\usepackage{CormorantGaramond}
\usepackage{charter}

% Colors
\usepackage{xcolor}
\definecolor{accentTitle}{HTML}{0e6e55}
\definecolor{accentText}{HTML}{0e6e55}
\definecolor{accentLine}{HTML}{a16f0b}

% Misc. Options
\pagestyle{fancy}
\fancyhf{}
\fancyfoot{}
\renewcommand{\headrulewidth}{0pt}
\renewcommand{\footrulewidth}{0pt}
\urlstyle{same}

% Adjust Margins
\addtolength{\oddsidemargin}{-0.6in}
\addtolength{\evensidemargin}{-0.4in}
\addtolength{\textwidth}{1.1in}
\addtolength{\topmargin}{-0.9in}
\addtolength{\textheight}{1.8in}

\setlength{\multicolsep}{-4.0pt}
\setlength{\columnsep}{-1pt}
\setlength{\tabcolsep}{0pt}
\setlength{\footskip}{3.7pt}
\raggedbottom
\raggedright

% ATS Readability
\input{glyphtounicode}
\pdfgentounicode=1

%-----------------%
% Custom Commands %
%-----------------%

% Centered title along with subtitle between HR
\newcommand{\documentTitle}[2]{
  \begin{center}
    {\Huge\color{accentTitle} #1}
    \vspace{10pt}
    {\color{accentLine} \hrule}
    \vspace{2pt}
    \footnotesize{#2}
    \vspace{2pt}
    {\color{accentLine} \hrule}
  \end{center}
}

% Create a footer with correct padding
\newcommand{\documentFooter}[1]{
  \setlength{\footskip}{10.25pt}
  \fancyfoot[C]{\footnotesize #1}
}

% Simple wrapper to set up page numbering
\newcommand{\numberedPages}{
  \documentFooter{\thepage/\pageref{LastPage}}
}

% Section heading with horizontal rule
\titleformat{\section}{
  \vspace{-5pt}
  \color{accentText}
  \raggedright\large\bfseries
}{}{0em}{}[\color{accentLine}\titlerule]

% Section heading with separator and content on same line
\newcommand{\tinysection}[1]{
  \phantomsection
  \addcontentsline{toc}{section}{#1}
  {\large{\bfseries\color{accentText}#1} {\color{accentLine} |}}
}

% Indented line with arguments left/right aligned in document
\newcommand{\heading}[2]{
  \hspace{10pt}#1\hfill#2\\
}

% Adds \textbf to \heading
\newcommand{\headingBf}[2]{
  \heading{\textbf{#1}}{\textbf{#2}}
}

% Adds \textit to \heading
\newcommand{\headingIt}[2]{
  \heading{\textit{#1}}{\textit{#2}}
}

% Template for itemized lists
\newenvironment{resume_list}{
  \vspace{-6pt}
  \begin{itemize}[itemsep=-2px, parsep=1pt, leftmargin=30pt]
}{
  \end{itemize}
}

% Formats an item to use as an itemized title
\newcommand{\itemTitle}[1]{
  \item[] \underline{#1}\vspace{4pt}
}

% Bullets used in itemized lists
\renewcommand\labelitemi{--}

%% END_FILE: mteck.sty
%%%%%%%%%%%%%%%%%%%%%%

%===================%
% T Suman Yadav's Resume - Microsoft Optimized %
%===================%

\begin{document}

  %---------%
  % Heading %
  %---------%
\documentTitle{T Suman Yadav}{
    \href{https://maps.app.goo.gl/jCFv78p8v8LVs3HY8}{\faMapMarker\ AMARAVATI, ANDHRA PRADESH} $ | $
    \href{tel:+917997596790}{\faPhone\ +91 7997596790} $ | $
    \href{mailto:<EMAIL>}{\faEnvelope\ <EMAIL>} $ | $
    \href{https://www.linkedin.com/in/tati-suman-yadav-938569351/}{\faLinkedin\ LinkedIn} $ | $
    \href{https://github.com/sumancoder-cloud}{\faGithub\ GitHub} $ | $
    \href{https://leetcode.com/u/ZRPwc0aAc3/}{\faCode\ Leetcode}
}

  \section*{Summary}
Computer Science student with strong fundamentals in data structures, algorithms, and software engineering. Experienced building scalable software solutions and collaborating in team environments. Passionate about solving complex problems through innovative engineering and empowering organizations through technology.

  %---------%
  % Education %
  %---------%
  \section{Education}

  \headingBf{SRM UNIVERSITY-AP}{Expected May 2027}
  \headingIt{Bachelor of Technology in Computer Science and Engineering}{CGPA: 9.28/10.0}

\section{Experience}

\begin{itemize}[itemsep=-2px, parsep=1pt, leftmargin=20pt]

  \item[]\textbf{Software Engineer Intern, Addwise Tech Innovations (w/ NighaTech Global Pvt. Ltd.), Remote}
  \hfill \textit{Dec 2024 – Present}

    \item Built scalable full-stack MERN solutions, improving system performance by 45\% through optimized APIs.
    \item Collaborated with cross-functional teams to deliver innovative software features meeting user requirements.
    \item Developed secure authentication systems using JWT and implemented engineering best practices.
    \item Demonstrated strong problem-solving skills and ability to quickly learn new technologies in team environment.

\end{itemize}

  %--------%
  % Skills %
  %--------%
\section{Technical Skills}

\begin{itemize}[itemsep=-2px, parsep=1pt, leftmargin=80pt]
  \item[\textbf{Programming:}] Java, Python, C++, JavaScript, TypeScript
  \item[\textbf{Data Structures \& Algorithms:}] Arrays, Trees, Graphs, Dynamic Programming, Sorting, Searching
  \item[\textbf{Web Development:}] React.js, Node.js, Express.js, HTML5, CSS3, RESTful APIs
  \item[\textbf{Database \& Tools:}] MongoDB, PostgreSQL, Git, GitHub, VS Code, CI/CD
  \item[\textbf{Technologies:}] TensorFlow, OpenCV, JWT Authentication, System Design
\end{itemize}

  %--------%
  % Projects %
  %--------%
\section{Projects}

\begin{itemize}[itemsep=-2px, parsep=1pt, leftmargin=20pt]

\item[] \textbf{GPS Tracker Application with Real-Time Location Monitoring}
\href{https://github.com/sumancoder-cloud/GPSTRACKER}{\textcolor{blue}{[GitHub]}}
  \hfill \textit{Dec 2024 – Present} \\
  \textit{MERN Stack, Google Maps API, QR Code Integration, MongoDB, JWT Authentication} \\

    \item Built full-stack GPS tracking system with real-time location monitoring and QR code device association.
    \item Implemented role-based dashboards (Admin/Super Admin/User) with secure authentication and MongoDB integration.
    \item Developed dynamic map visualization showing real-time device movement paths with coordinate updates via APIs.

  \item[] \textbf{AI-Powered Driver Safety System}
\href{https://github.com/sumancoder-cloud/AI-DROWNINESS-DETECTION-AND-PREVENTION-SYSTEM}{\textcolor{blue}{[GitHub]}}
  \hfill \textit{Mar–Apr 2025} \\
  \textit{Python, OpenCV, Computer Vision, Multi-threading} \\

    \item Engineered real-time driver drowsiness detection system using computer vision and IoT integration.
    \item Implemented efficient algorithms for video processing achieving 90\%+ accuracy through systematic testing.
    \item Applied engineering best practices including modular design and comprehensive error handling.

  \item[] \textbf{Machine Learning Fake News Detection}
\href{https://github.com/sumancoder-cloud/Fakenewsdetector-}{\textcolor{blue}{[GitHub]}}
\hfill \textit{Jan–Feb 2025} \\
\textit{Python, Scikit-learn, NLP, Flask, Model Optimization}

  \item Developed end-to-end ML solution for automated fake news classification using multiple algorithms.
  \item Optimized feature extraction and model performance through hyperparameter tuning and comparative analysis.

\end{itemize}

\section{Achievements}

\begin{itemize}[itemsep=-2px, parsep=1pt, leftmargin=20pt]
  \item \textbf{Problem Solving:} Solved 250+ DSA problems on LeetCode, GeeksforGeeks, and HackerRank.
  \item \textbf{Certifications:} AI/ML certification (Vodafone Idea Foundation), TCS iON Career Edge certification.
  \item \textbf{Hackathons:} Reached final rounds in Codeathon by NeoColab and Hack4SDG by AIESEC Amaravati.
  \item \textbf{Leadership:} Selected among top 50+ teams in Inter-University Cricket League, active university volunteer.
\end{itemize}

\end{document}
