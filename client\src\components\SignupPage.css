.signup-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%);
  padding: 1rem;
}

.signup-container {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
}

.signup-card {
  background: white;
  border-radius: 15px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  position: relative;
  overflow: hidden;
}

.signup-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #6f42c1, #9c27b0);
}

.signup-header {
  text-align: center;
  margin-bottom: 1.5rem;
}

.signup-header .logo {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.signup-header h1 {
  color: #2c3e50;
  font-size: 1.5rem;
  margin-bottom: 0.25rem;
  font-weight: 600;
}

.signup-header p {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 0;
}

.signup-form {
  max-width: 100%;
  margin: 0 auto;
}

.form-section {
  background: #f8f9fa;
  border-radius: 10px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.form-section h3 {
  color: #2c3e50;
  font-size: 1rem;
  margin-bottom: 0.75rem;
  font-weight: 600;
}

.form-group {
  margin-bottom: 0.75rem;
}

.form-group label {
  color: #2c3e50;
  font-weight: 500;
  margin-bottom: 0.25rem;
  font-size: 0.9rem;
}

.form-control {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 0.5rem 0.75rem;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.form-control:focus {
  border-color: #6f42c1;
  box-shadow: 0 0 0 0.15rem rgba(111, 66, 193, 0.25);
}

.form-control::placeholder {
  color: #adb5bd;
  font-size: 0.85rem;
}

.form-text {
  color: #6c757d;
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

.terms-group {
  margin: 1rem 0;
  font-size: 0.85rem;
}

.terms-link {
  color: #6f42c1;
  text-decoration: none;
  font-weight: 500;
}

.terms-link:hover {
  text-decoration: underline;
}

.form-actions {
  text-align: center;
  margin-top: 1rem;
}

.signup-button {
  background: linear-gradient(90deg, #6f42c1, #9c27b0);
  border: none;
  border-radius: 8px;
  color: white;
  font-size: 0.95rem;
  font-weight: 600;
  padding: 0.6rem 1.5rem;
  width: 100%;
  max-width: 200px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.signup-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(111, 66, 193, 0.3);
}

.signup-button:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.spinner {
  display: inline-block;
  width: 0.8rem;
  height: 0.8rem;
  border: 2px solid #ffffff;
  border-radius: 50%;
  border-top-color: transparent;
  margin-right: 0.5rem;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.login-link {
  margin-top: 1rem;
  color: #666;
  font-size: 0.85rem;
}

.login-link .link {
  color: #6f42c1;
  text-decoration: none;
  font-weight: 500;
}

.login-link .link:hover {
  text-decoration: underline;
}

.signup-alert {
  border-radius: 8px;
  margin-bottom: 1rem;
  border: none;
  background-color: #fff3f3;
  color: #dc3545;
  padding: 0.75rem;
  font-weight: 500;
  font-size: 0.85rem;
}

/* Responsive Design */
@media (max-width: 576px) {
  .signup-card {
    padding: 1rem;
  }

  .signup-header h1 {
    font-size: 1.25rem;
  }

  .form-section {
    padding: 0.75rem;
  }

  .signup-button {
    max-width: 100%;
  }
}

/* Form validation styles */
.form-control.is-invalid {
  border-color: #dc3545;
  background-image: none;
}

.form-control.is-valid {
  border-color: #6f42c1;
  background-image: none;
}

.invalid-feedback {
  color: #dc3545;
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

/* Custom select styling */
.form-select {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 0.5rem 0.75rem;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 16 16'%3E%3Cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 12px 8px;
  appearance: none;
}

.form-select:focus {
  border-color: #6f42c1;
  box-shadow: 0 0 0 0.15rem rgba(111, 66, 193, 0.25);
}

/* Custom checkbox styling */
.form-check-input {
  width: 1rem;
  height: 1rem;
  margin-top: 0.2em;
  border: 1px solid #e9ecef;
  border-radius: 0.25em;
  transition: all 0.3s ease;
}

.form-check-input:checked {
  background-color: #6f42c1;
  border-color: #6f42c1;
}

.form-check-input:focus {
  border-color: #6f42c1;
  box-shadow: 0 0 0 0.15rem rgba(111, 66, 193, 0.25);
}

/* Animation for form sections */
.form-section {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
} 