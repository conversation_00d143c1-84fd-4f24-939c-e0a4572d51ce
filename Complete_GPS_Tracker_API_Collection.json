{"info": {"name": "Complete GPS Tracker API Collection", "description": "Complete API collection for GPS Tracker with Authentication, GPS APIs, and all endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Authentication APIs", "item": [{"name": "Auth Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/auth/health", "host": ["{{baseUrl}}"], "path": ["api", "auth", "health"]}}}, {"name": "Register New User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"postman_user\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"PostmanTest123!\",\n  \"confirmPassword\": \"PostmanTest123!\",\n  \"firstName\": \"Postman\",\n  \"lastName\": \"User\",\n  \"company\": \"GPS Tracker Inc\",\n  \"phone\": \"+91 **********\",\n  \"role\": \"user\",\n  \"agreeToTerms\": true\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/register", "host": ["{{baseUrl}}"], "path": ["api", "auth", "register"]}}}, {"name": "Login User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"testadmin\",\n  \"password\": \"Admin123!\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/login", "host": ["{{baseUrl}}"], "path": ["api", "auth", "login"]}}}, {"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"testadmin\",\n  \"password\": \"Admin123!\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/login", "host": ["{{baseUrl}}"], "path": ["api", "auth", "login"]}}}]}, {"name": "GPS Tracking APIs", "item": [{"name": "GPS Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/gps/health", "host": ["{{baseUrl}}"], "path": ["api", "gps", "health"]}}}, {"name": "Update Device Location (Kadapa)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"deviceId\": \"QR112562854\",\n  \"deviceName\": \"Puppy GPS Tracker\",\n  \"latitude\": 14.4673,\n  \"longitude\": 78.8242,\n  \"accuracy\": 5,\n  \"speed\": 2.5,\n  \"heading\": 45\n}"}, "url": {"raw": "{{baseUrl}}/api/gps/location", "host": ["{{baseUrl}}"], "path": ["api", "gps", "location"]}}}, {"name": "Update Device Location (Proddatur)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"deviceId\": \"QR112562854\",\n  \"deviceName\": \"Puppy GPS Tracker\",\n  \"latitude\": 14.7300,\n  \"longitude\": 78.5500,\n  \"accuracy\": 3,\n  \"speed\": 1.8,\n  \"heading\": 90\n}"}, "url": {"raw": "{{baseUrl}}/api/gps/location", "host": ["{{baseUrl}}"], "path": ["api", "gps", "location"]}}}, {"name": "Get Device Current Location", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/gps/device/QR112562854", "host": ["{{baseUrl}}"], "path": ["api", "gps", "device", "QR112562854"]}}}, {"name": "Get Device Path History", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/gps/path/QR112562854", "host": ["{{baseUrl}}"], "path": ["api", "gps", "path", "QR112562854"]}}}, {"name": "Get All Tracked Devices", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/gps/devices", "host": ["{{baseUrl}}"], "path": ["api", "gps", "devices"]}}}, {"name": "Simulate Device Movement", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"startLat\": 14.7300,\n  \"startLng\": 78.5500,\n  \"endLat\": 14.7350,\n  \"endLng\": 78.5550,\n  \"steps\": 15,\n  \"deviceName\": \"Puppy GPS Tracker\"\n}"}, "url": {"raw": "{{baseUrl}}/api/gps/simulate/QR112562854", "host": ["{{baseUrl}}"], "path": ["api", "gps", "simulate", "QR112562854"]}}}]}, {"name": "Device Management APIs", "item": [{"name": "Get All Devices", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/devices", "host": ["{{baseUrl}}"], "path": ["api", "devices"]}}}, {"name": "Create New Device", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"deviceName\": \"Test Device\",\n  \"deviceType\": \"GPS Tracker\",\n  \"description\": \"Test device for Postman\"\n}"}, "url": {"raw": "{{baseUrl}}/api/devices", "host": ["{{baseUrl}}"], "path": ["api", "devices"]}}}]}, {"name": "QR Code APIs", "item": [{"name": "Generate QR Code", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"deviceName\": \"Postman Test Device\",\n  \"deviceType\": \"GPS Tracker\",\n  \"description\": \"Generated via Postman\"\n}"}, "url": {"raw": "{{baseUrl}}/api/qr/generate", "host": ["{{baseUrl}}"], "path": ["api", "qr", "generate"]}}}, {"name": "Get All QR Codes", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/qr", "host": ["{{baseUrl}}"], "path": ["api", "qr"]}}}]}], "variable": [{"key": "baseUrl", "value": "http://localhost:5001", "type": "string"}]}