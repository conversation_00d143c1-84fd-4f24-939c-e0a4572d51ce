{"name": "client", "version": "0.1.0", "private": true, "dependencies": {"@react-google-maps/api": "^2.20.7", "@testing-library/dom": "^8.11.3", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^12.1.5", "@testing-library/user-event": "^13.5.0", "addwise-gps-tracker": "file:..", "axios": "^1.6.7", "bootstrap": "^5.3.6", "html5-qrcode": "^2.3.8", "jsqr": "^1.4.0", "qrcode": "^1.5.4", "react": "^17.0.2", "react-bootstrap": "^2.10.10", "react-dom": "^17.0.2", "react-qr-reader": "^3.0.0-beta-1", "react-router-dom": "^6.22.0", "react-scripts": "5.0.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"assert": "^2.1.0", "browserify-zlib": "^0.2.0", "buffer": "^6.0.3", "crypto-browserify": "^3.12.0", "https-browserify": "^1.0.0", "os-browserify": "^0.3.0", "path-browserify": "^1.0.1", "process": "^0.11.10", "react-app-rewired": "^2.2.1", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "url": "^0.11.3", "util": "^0.12.5"}}