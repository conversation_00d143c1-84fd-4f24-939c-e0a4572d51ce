const express = require('express');
const router = express.Router();
const Device = require('../models/Device');
const User = require('../models/User');
const { authenticate, authorize } = require('../middleware/auth');

// POST /api/devices/user-upload - User uploads a device
router.post('/user-upload', authenticate, async (req, res) => {
  try {
    const { deviceCode, description, purpose, uploadMethod, qrImageName } = req.body;
    const userId = req.user.id;

    // Validate required fields
    if (!deviceCode || !description || !purpose) {
      return res.status(400).json({
        success: false,
        message: 'Device code, description, and purpose are required'
      });
    }

    // Check if device code already exists for this user
    const existingDevice = await Device.findOne({
      deviceId: deviceCode,
      assignedTo: userId
    });

    if (existingDevice) {
      return res.status(400).json({
        success: false,
        message: 'Device already registered',
        device: existingDevice
      });
    }

    // Create new device
    const newDevice = new Device({
      deviceId: deviceCode,
      name: description,
      description: description,
      purpose: purpose,
      uploadMethod: uploadMethod,
      qrImageName: qrImageName,
      assignedTo: userId,
      status: 'pending_approval',
      createdAt: new Date(),
      lastSeen: new Date()
    });

    await newDevice.save();

    res.json({
      success: true,
      message: 'Device uploaded successfully',
      device: newDevice
    });

  } catch (error) {
    console.error('Error uploading device:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// GET /api/devices/my-devices - Get user's devices
router.get('/my-devices', authenticate, async (req, res) => {
  try {
    const userId = req.user.id;

    const devices = await Device.find({ assignedTo: userId })
      .populate('assignedTo', 'username email')
      .sort({ createdAt: -1 });

    const formattedDevices = devices.map(device => ({
      id: device._id,
      deviceCode: device.deviceId,
      description: device.description || device.name,
      purpose: device.purpose,
      uploadMethod: device.uploadMethod,
      qrImageName: device.qrImageName,
      status: device.status,
      addedDate: device.createdAt,
      addedBy: device.assignedTo?.username
    }));

    res.json({
      success: true,
      devices: formattedDevices
    });

  } catch (error) {
    console.error('Error fetching user devices:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// GET /api/devices/check/:deviceCode - Check if device exists
router.get('/check/:deviceCode', authenticate, async (req, res) => {
  try {
    const { deviceCode } = req.params;
    const userId = req.user.id;

    const device = await Device.findOne({
      deviceId: deviceCode,
      assignedTo: userId
    }).populate('assignedTo', 'username email');

    if (device) {
      const formattedDevice = {
        id: device._id,
        deviceCode: device.deviceId,
        description: device.description || device.name,
        purpose: device.purpose,
        uploadMethod: device.uploadMethod,
        status: device.status,
        addedDate: device.createdAt,
        addedBy: device.assignedTo?.username
      };

      res.json({
        success: true,
        exists: true,
        device: formattedDevice
      });
    } else {
      res.json({
        success: true,
        exists: false,
        device: null
      });
    }

  } catch (error) {
    console.error('Error checking device:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// GET /api/devices/admin/user-uploads - Admin view of all user uploads
router.get('/admin/user-uploads', authenticate, async (req, res) => {
  try {
    // Check if user is admin or superadmin
    if (req.user.role !== 'admin' && req.user.role !== 'superadmin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Admin privileges required.'
      });
    }

    const devices = await Device.find({})
      .populate('assignedTo', 'username email')
      .sort({ createdAt: -1 });

    const formattedUploads = devices.map(device => ({
      id: device._id,
      deviceCode: device.deviceId,
      description: device.description || device.name,
      purpose: device.purpose,
      uploadMethod: device.uploadMethod,
      qrImageName: device.qrImageName,
      status: device.status,
      addedDate: device.createdAt,
      userId: device.assignedTo?._id,
      userName: device.assignedTo?.username,
      userEmail: device.assignedTo?.email
    }));

    res.json({
      success: true,
      uploads: formattedUploads
    });

  } catch (error) {
    console.error('Error fetching admin device uploads:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// PUT /api/devices/admin/approve/:deviceId - Admin approve device
router.put('/admin/approve/:deviceId', authenticate, async (req, res) => {
  try {
    // Check if user is admin or superadmin
    if (req.user.role !== 'admin' && req.user.role !== 'superadmin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Admin privileges required.'
      });
    }

    const { deviceId } = req.params;

    const device = await Device.findByIdAndUpdate(
      deviceId,
      { 
        status: 'approved',
        approvedBy: req.user.id,
        approvedAt: new Date()
      },
      { new: true }
    ).populate('assignedTo', 'username email');

    if (!device) {
      return res.status(404).json({
        success: false,
        message: 'Device not found'
      });
    }

    res.json({
      success: true,
      message: 'Device approved successfully',
      device: device
    });

  } catch (error) {
    console.error('Error approving device:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// PUT /api/devices/admin/reject/:deviceId - Admin reject device
router.put('/admin/reject/:deviceId', authenticate, async (req, res) => {
  try {
    // Check if user is admin or superadmin
    if (req.user.role !== 'admin' && req.user.role !== 'superadmin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Admin privileges required.'
      });
    }

    const { deviceId } = req.params;
    const { reason } = req.body;

    const device = await Device.findByIdAndUpdate(
      deviceId,
      { 
        status: 'rejected',
        rejectedBy: req.user.id,
        rejectedAt: new Date(),
        rejectionReason: reason
      },
      { new: true }
    ).populate('assignedTo', 'username email');

    if (!device) {
      return res.status(404).json({
        success: false,
        message: 'Device not found'
      });
    }

    res.json({
      success: true,
      message: 'Device rejected successfully',
      device: device
    });

  } catch (error) {
    console.error('Error rejecting device:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

module.exports = router;
