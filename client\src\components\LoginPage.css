.login-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%);
  padding: 1rem;
}

.login-container {
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
}

.login-card {
  background: white;
  border-radius: 15px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  position: relative;
  overflow: hidden;
}

.login-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #6f42c1, #9c27b0);
}

.login-header {
  text-align: center;
  margin-bottom: 1.5rem;
}

.login-header .logo {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
}

.login-header h1 {
  color: #2c3e50;
  font-size: 1.5rem;
  margin-bottom: 0.25rem;
  font-weight: 600;
}

.login-header p {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 0;
}

.login-form {
  max-width: 100%;
  margin: 0 auto;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  color: #2c3e50;
  font-weight: 500;
  margin-bottom: 0.25rem;
  font-size: 0.9rem;
}

.form-control {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 0.5rem 0.75rem;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.form-control:focus {
  border-color: #6f42c1;
  box-shadow: 0 0 0 0.15rem rgba(111, 66, 193, 0.25);
}

.form-control::placeholder {
  color: #adb5bd;
  font-size: 0.85rem;
}

.form-text {
  color: #6c757d;
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

.login-button {
  background: linear-gradient(90deg, #6f42c1, #9c27b0);
  border: none;
  border-radius: 8px;
  color: white;
  font-size: 0.95rem;
  font-weight: 600;
  padding: 0.6rem 1.5rem;
  width: 100%;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.login-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(111, 66, 193, 0.3);
}

.login-button:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.spinner {
  display: inline-block;
  width: 0.8rem;
  height: 0.8rem;
  border: 2px solid #ffffff;
  border-radius: 50%;
  border-top-color: transparent;
  margin-right: 0.5rem;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.signup-link {
  text-align: center;
  margin-top: 1rem;
  color: #666;
  font-size: 0.85rem;
}

.signup-link .link {
  color: #6f42c1;
  text-decoration: none;
  font-weight: 500;
}

.signup-link .link:hover {
  text-decoration: underline;
}

.login-alert {
  border-radius: 8px;
  margin-bottom: 1rem;
  border: none;
  background-color: #fff3f3;
  color: #dc3545;
  padding: 0.75rem;
  font-weight: 500;
  font-size: 0.85rem;
}

.role-info {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  border-left: 4px solid #6f42c1;
}

.role-info h4 {
  color: #2c3e50;
  font-size: 0.95rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.role-info p {
  color: #666;
  font-size: 0.85rem;
  margin-bottom: 0;
}

.role-icon {
  font-size: 1.2rem;
  margin-right: 0.5rem;
}

/* Responsive Design */
@media (max-width: 576px) {
  .login-card {
    padding: 1rem;
  }

  .login-header h1 {
    font-size: 1.25rem;
  }
}

/* Form validation styles */
.form-control.is-invalid {
  border-color: #dc3545;
  background-image: none;
}

.form-control.is-valid {
  border-color: #6f42c1;
  background-image: none;
}

.invalid-feedback {
  color: #dc3545;
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

/* Animation for login card */
.login-card {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
} 