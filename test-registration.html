<!DOCTYPE html>
<html>
<head>
    <title>Test Registration</title>
</head>
<body>
    <h1>Test User Registration</h1>
    <button onclick="registerTestUser()">Register Test Admin User</button>
    <div id="result"></div>

    <script>
        async function registerTestUser() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Registering user...';

            try {
                const response = await fetch('http://localhost:5001/api/auth/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'testadmin',
                        email: '<EMAIL>',
                        password: 'admin123',
                        confirmPassword: 'admin123',
                        firstName: 'Test',
                        lastName: 'Admin',
                        role: 'admin',
                        company: 'GPS Tracker Inc',
                        phone: '+91 9876543210',
                        agreeToTerms: true
                    })
                });

                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <h3>✅ Registration Successful!</h3>
                        <p>Username: testadmin</p>
                        <p>Password: admin123</p>
                        <p>Role: admin</p>
                        <p>Now try logging in at: <a href="http://localhost:3000/login/admin" target="_blank">http://localhost:3000/login/admin</a></p>
                    `;
                } else {
                    resultDiv.innerHTML = `<h3>❌ Registration Failed</h3><p>${data.message}</p>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<h3>❌ Error</h3><p>${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
