{"info": {"name": "GPS Location Testing Collection", "description": "Test location changes and track device movement in real-time", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "1. Start Journey - <PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"deviceId\": \"QR112562854\",\n  \"deviceName\": \"Puppy GPS Tracker\",\n  \"latitude\": 14.4673,\n  \"longitude\": 78.8242,\n  \"accuracy\": 5,\n  \"speed\": 0,\n  \"heading\": 0\n}"}, "url": {"raw": "{{baseUrl}}/api/gps/location", "host": ["{{baseUrl}}"], "path": ["api", "gps", "location"]}}}, {"name": "2. Move to Highway", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"deviceId\": \"QR112562854\",\n  \"deviceName\": \"Puppy GPS Tracker\",\n  \"latitude\": 14.5200,\n  \"longitude\": 78.8500,\n  \"accuracy\": 3,\n  \"speed\": 45,\n  \"heading\": 315\n}"}, "url": {"raw": "{{baseUrl}}/api/gps/location", "host": ["{{baseUrl}}"], "path": ["api", "gps", "location"]}}}, {"name": "3. Midway Point", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"deviceId\": \"QR112562854\",\n  \"deviceName\": \"Puppy GPS Tracker\",\n  \"latitude\": 14.6000,\n  \"longitude\": 78.7000,\n  \"accuracy\": 4,\n  \"speed\": 55,\n  \"heading\": 290\n}"}, "url": {"raw": "{{baseUrl}}/api/gps/location", "host": ["{{baseUrl}}"], "path": ["api", "gps", "location"]}}}, {"name": "4. <PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"deviceId\": \"QR112562854\",\n  \"deviceName\": \"Puppy GPS Tracker\",\n  \"latitude\": 14.7300,\n  \"longitude\": 78.5500,\n  \"accuracy\": 2,\n  \"speed\": 25,\n  \"heading\": 270\n}"}, "url": {"raw": "{{baseUrl}}/api/gps/location", "host": ["{{baseUrl}}"], "path": ["api", "gps", "location"]}}}, {"name": "5. Stop at Destination", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"deviceId\": \"QR112562854\",\n  \"deviceName\": \"Puppy GPS Tracker\",\n  \"latitude\": 14.7300,\n  \"longitude\": 78.5500,\n  \"accuracy\": 1,\n  \"speed\": 0,\n  \"heading\": 0\n}"}, "url": {"raw": "{{baseUrl}}/api/gps/location", "host": ["{{baseUrl}}"], "path": ["api", "gps", "location"]}}}, {"name": "Check Current Location", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/gps/device/QR112562854", "host": ["{{baseUrl}}"], "path": ["api", "gps", "device", "QR112562854"]}}}, {"name": "Get Complete Path History", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/gps/path/QR112562854", "host": ["{{baseUrl}}"], "path": ["api", "gps", "path", "QR112562854"]}}}, {"name": "Get All Tracked Devices", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/gps/devices", "host": ["{{baseUrl}}"], "path": ["api", "gps", "devices"]}}}], "variable": [{"key": "baseUrl", "value": "http://localhost:5001", "type": "string"}]}