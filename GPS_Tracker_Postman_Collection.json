{"info": {"name": "GPS Tracker API - Complete Test Suite", "description": "Complete API testing collection for GPS Tracker application with MongoDB integration", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "🏥 Health & Status", "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3001/api/test", "protocol": "http", "host": ["localhost"], "port": "3001", "path": ["api", "test"]}}}]}, {"name": "🔐 Authentication", "item": [{"name": "Login - <PERSON><PERSON>r", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "http://localhost:3001/api/login", "protocol": "http", "host": ["localhost"], "port": "3001", "path": ["api", "login"]}}}]}, {"name": "📊 Dashboard APIs", "item": [{"name": "Dashboard Stats", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3001/api/dashboard/stats", "protocol": "http", "host": ["localhost"], "port": "3001", "path": ["api", "dashboard", "stats"]}}}, {"name": "Dashboard Users", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3001/api/dashboard/users", "protocol": "http", "host": ["localhost"], "port": "3001", "path": ["api", "dashboard", "users"]}}}, {"name": "Dashboard Devices", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3001/api/dashboard/devices", "protocol": "http", "host": ["localhost"], "port": "3001", "path": ["api", "dashboard", "devices"]}}}, {"name": "Dashboard Activities", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3001/api/dashboard/activities", "protocol": "http", "host": ["localhost"], "port": "3001", "path": ["api", "dashboard", "activities"]}}}]}, {"name": "🤖 AI Integration", "item": [{"name": "AI Chat Test", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"message\": \"Hello GPS Tracker API! Testing AI integration.\"\n}"}, "url": {"raw": "http://localhost:3001/v1/chat/completions", "protocol": "http", "host": ["localhost"], "port": "3001", "path": ["v1", "chat", "completions"]}}}]}]}