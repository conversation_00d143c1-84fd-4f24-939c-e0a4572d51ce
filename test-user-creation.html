<!DOCTYPE html>
<html>
<head>
    <title>Create Test User - GPS Tracker</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .result { margin: 10px 0; padding: 15px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button { padding: 12px 24px; margin: 10px 5px; cursor: pointer; background: #007bff; color: white; border: none; border-radius: 4px; font-size: 16px; }
        button:hover { background: #0056b3; }
        .credentials { background: #e9ecef; padding: 15px; border-radius: 5px; margin: 10px 0; }
        h1 { color: #333; text-align: center; }
        h3 { color: #495057; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 GPS Tracker - Fix Login Issue</h1>
        
        <h3>Step 1: Create Test User</h3>
        <button onclick="createTestUser()">Create Test User</button>
        <div id="createResult"></div>

        <h3>Step 2: Test Login</h3>
        <button onclick="testLogin()">Test Login</button>
        <div id="loginResult"></div>

        <div class="credentials">
            <h3>🔑 Test Credentials:</h3>
            <strong>Username:</strong> testuser<br>
            <strong>Password:</strong> Test123!<br>
            <strong>Role:</strong> user<br>
            <strong>Login URL:</strong> <a href="http://localhost:3000/login/user" target="_blank">http://localhost:3000/login/user</a>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5001/api';

        async function createTestUser() {
            const resultDiv = document.getElementById('createResult');
            resultDiv.innerHTML = '<div class="warning">Creating test user...</div>';

            const userData = {
                username: 'testuser',
                email: '<EMAIL>',
                password: 'Test123!',
                confirmPassword: 'Test123!',
                firstName: 'Test',
                lastName: 'User',
                role: 'user',
                company: 'GPS Tracker Inc',
                phone: '+91 9876543210',
                agreeToTerms: true
            };

            try {
                const response = await fetch(`${API_BASE}/auth/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(userData)
                });

                const data = await response.json();
                
                if (response.ok && data.success) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <h4>✅ User Created Successfully!</h4>
                            <p><strong>Username:</strong> testuser</p>
                            <p><strong>Password:</strong> Test123!</p>
                            <p><strong>Role:</strong> user</p>
                            <p>You can now test the login!</p>
                        </div>
                    `;
                } else {
                    if (data.message && data.message.includes('already exists')) {
                        resultDiv.innerHTML = `
                            <div class="result warning">
                                <h4>⚠️ User Already Exists</h4>
                                <p>The test user already exists. You can try logging in with:</p>
                                <p><strong>Username:</strong> testuser</p>
                                <p><strong>Password:</strong> Test123!</p>
                            </div>
                        `;
                    } else {
                        resultDiv.innerHTML = `<div class="result error">❌ Registration Failed: ${data.message}</div>`;
                    }
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">❌ Network Error: ${error.message}</div>`;
            }
        }

        async function testLogin() {
            const resultDiv = document.getElementById('loginResult');
            resultDiv.innerHTML = '<div class="warning">Testing login...</div>';

            const loginData = {
                username: 'testuser',
                password: 'Test123!'
            };

            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(loginData)
                });

                const data = await response.json();
                
                if (response.ok && data.success) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <h4>✅ Login Successful!</h4>
                            <p><strong>Welcome:</strong> ${data.data.user.firstName} ${data.data.user.lastName}</p>
                            <p><strong>Role:</strong> ${data.data.user.role}</p>
                            <p><strong>Email:</strong> ${data.data.user.email}</p>
                            <p>🎉 Authentication is working! You can now login to the main app.</p>
                            <p><a href="http://localhost:3000/login/user" target="_blank">Go to Login Page</a></p>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="result error">❌ Login Failed: ${data.message}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">❌ Network Error: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
