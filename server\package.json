{"name": "gps-tracker-backend", "version": "1.0.0", "description": "Node.js backend for GPS Tracker application", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["gps", "tracker", "nodejs", "express", "mongodb"], "author": "<PERSON><PERSON>", "license": "MIT", "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "nodemailer": "^7.0.4", "qrcode": "^1.5.3", "validator": "^13.11.0"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=14.0.0"}}