<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Real-Time GPS Testing</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background-color: #d4edda; color: #155724; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .warning { background-color: #fff3cd; color: #856404; }
        .error { background-color: #f8d7da; color: #721c24; }
        .coordinates {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
        }
        .button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background-color: #0056b3;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗺️ Real-Time GPS Coordinate Testing</h1>
        
        <div class="status info">
            <strong>📡 Testing Device:</strong> QR112562854 (Test GPS Device)
        </div>
        
        <div class="status info">
            <strong>🌐 API Endpoint:</strong> http://localhost:5001/api/gps/location
        </div>
        
        <div id="connectionStatus" class="status warning">
            <strong>🔄 Status:</strong> Checking connection...
        </div>
        
        <div class="coordinates">
            <h3>📍 Current Device Location:</h3>
            <div id="currentLocation">Loading...</div>
        </div>
        
        <div>
            <h3>🎯 Test Coordinate Updates:</h3>
            <button class="button" onclick="sendCoordinate(14.7300, 78.5500, 'Start Position')">📍 Start Position</button>
            <button class="button" onclick="sendCoordinate(14.7350, 78.5550, 'Move North-East')">🔄 Move North-East</button>
            <button class="button" onclick="sendCoordinate(14.7400, 78.5600, 'Continue Movement')">➡️ Continue Movement</button>
            <button class="button" onclick="sendCoordinate(14.7500, 78.5700, 'Final Position')">🏁 Final Position</button>
        </div>
        
        <div>
            <h3>📊 Activity Log:</h3>
            <div id="log" class="log"></div>
        </div>
        
        <div class="status info">
            <strong>💡 Instructions:</strong><br>
            1. Open your GPS Tracker app at <a href="http://localhost:3000" target="_blank">http://localhost:3000</a><br>
            2. Navigate to the Real-Time Path Map for device QR112562854<br>
            3. Click the coordinate buttons above to send updates<br>
            4. Watch the map update in real-time (every 2 seconds)
        </div>
    </div>

    <script>
        const deviceId = 'QR112562854';
        const apiBase = 'http://localhost:5001';
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        async function checkConnection() {
            try {
                const response = await fetch(`${apiBase}/api/gps/health`);
                const data = await response.json();
                
                if (data.status === 'success') {
                    document.getElementById('connectionStatus').className = 'status success';
                    document.getElementById('connectionStatus').innerHTML = '<strong>✅ Status:</strong> Connected to GPS API';
                    log('✅ Successfully connected to GPS API');
                    loadCurrentLocation();
                } else {
                    throw new Error('API returned error status');
                }
            } catch (error) {
                document.getElementById('connectionStatus').className = 'status error';
                document.getElementById('connectionStatus').innerHTML = '<strong>❌ Status:</strong> Cannot connect to GPS API';
                log('❌ Failed to connect to GPS API: ' + error.message);
            }
        }
        
        async function loadCurrentLocation() {
            try {
                const response = await fetch(`${apiBase}/api/gps/device/${deviceId}`);
                const data = await response.json();
                
                if (data.status === 'success') {
                    const device = data.data.device;
                    document.getElementById('currentLocation').innerHTML = `
                        <strong>Latitude:</strong> ${device.latitude}<br>
                        <strong>Longitude:</strong> ${device.longitude}<br>
                        <strong>Speed:</strong> ${device.speed} km/h<br>
                        <strong>Heading:</strong> ${device.heading}°<br>
                        <strong>Path Points:</strong> ${data.data.pathPoints}<br>
                        <strong>Last Update:</strong> ${new Date(data.data.lastUpdate).toLocaleString()}
                    `;
                    log(`📍 Current location: ${device.latitude}, ${device.longitude}`);
                } else {
                    document.getElementById('currentLocation').innerHTML = 'No location data available';
                    log('⚠️ No location data found for device');
                }
            } catch (error) {
                document.getElementById('currentLocation').innerHTML = 'Error loading location';
                log('❌ Error loading location: ' + error.message);
            }
        }
        
        async function sendCoordinate(lat, lng, description) {
            try {
                log(`📡 Sending coordinate update: ${description} (${lat}, ${lng})`);
                
                const response = await fetch(`${apiBase}/api/gps/location`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        deviceId: deviceId,
                        deviceName: 'Test GPS Device',
                        latitude: lat,
                        longitude: lng,
                        accuracy: 3,
                        speed: Math.round(Math.random() * 50) / 10,
                        heading: Math.round(Math.random() * 360)
                    })
                });
                
                const data = await response.json();
                
                if (data.status === 'success') {
                    log(`✅ ${description} updated successfully`);
                    setTimeout(loadCurrentLocation, 1000); // Refresh location after 1 second
                } else {
                    log(`❌ Failed to update ${description}: ${data.message}`);
                }
            } catch (error) {
                log(`❌ Error sending coordinate: ${error.message}`);
            }
        }
        
        // Auto-refresh current location every 3 seconds
        setInterval(loadCurrentLocation, 3000);
        
        // Initial connection check
        checkConnection();
    </script>
</body>
</html>
